import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/core/local_models/streak_model/streak_model.dart';
import 'package:cal/features/home/<USER>/widgets/streak_dialog.dart';
import 'package:cal/features/main/presentation/bloc/main_bloc.dart';
import 'package:cal/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

AppBar homeAppBar(BuildContext context) {
  return AppBar(
    forceMaterialTransparency: true,
    elevation: 0,
    automaticallyImplyLeading: false,
    title: const AppImage.asset(Assets.imagesOrangeAi, height: 24, width: 24),
    actions: [
      BlocBuilder<MainBloc, MainState>(
        builder: (context, state) {
          return InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: () => showDialog(context: context, builder: (_) => StreakDialog(bloc: context.read<MainBloc>())),
            child: Container(
              margin: const EdgeInsetsDirectional.symmetric(horizontal: 20),
              padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 4.0),
              decoration: BoxDecoration(
                color: context.onPrimaryColor,
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: Row(
                children: [
                  const Icon(Icons.local_fire_department, color: Colors.orange, size: 18.0),
                  const SizedBox(width: 4.0),
                  Text(
                    state.streaksNumber.toString(),
                    style: const TextStyle(color: Colors.black, fontSize: 16.0),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    ],
  );
}

int longestConsecutiveDays(List<StreakModel> dates) {
  if (dates.isEmpty) return 0;
  if (dates.length == 1) return 1;

  dates.sort((a, b) => a.streakDate.compareTo(b.streakDate));

  int maxLength = 1;
  int currentLength = 1;

  for (int i = 1; i < dates.length; i++) {
    final prev = dates[i - 1].streakDate;
    final curr = dates[i].streakDate;

    final difference = curr.difference(prev).inDays;

    if (difference == 1) {
      currentLength++;
      maxLength = currentLength > maxLength ? currentLength : maxLength;
    } else if (difference > 1) {
      currentLength = 1;
    }
  }
  return maxLength;
}
