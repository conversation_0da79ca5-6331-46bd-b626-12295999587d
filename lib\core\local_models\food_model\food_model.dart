import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'food_model.g.dart';

FoodModel foodModelFromJson(str) => FoodModel.fromJson(str);

@Collection(inheritance: false)
// ignore: must_be_immutable
class FoodModel extends Equatable {
  Id id = Isar.autoIncrement;

  late String? dish;
  late double? serving;
  late String? arabicName;
  late String? englishName;
  late int? calories;
  late double? protein;
  late double? carbs;
  late double? fat;
  late List<Ingredient> ingredientList;
  late bool? isHalal;

  late String? imagePath;
  late DateTime? date;

  late int? healthScore;

  @ignore
  bool isLoading = false;
  @ignore
  bool hasError;
  @ignore
  String? tempId;

  int? remoteLogId;

  FoodModel({
    this.dish,
    this.calories,
    this.healthScore,
    this.protein,
    this.carbs,
    this.fat,
    this.isHalal,
    this.serving,
    DateTime? date,
    this.imagePath,
    this.isLoading = false,
    this.englishName,
    this.arabicName,
    this.hasError = false,
    this.tempId,
    this.remoteLogId,
    this.ingredientList = const [],
  }) : date = date ?? DateTime.now();

  factory FoodModel.fromDatabaseModel(DatabaseFoodModel db) {
    return FoodModel(
      dish: db.dish ?? 'unknown',
      calories: db.calories ?? 0,
      protein: db.protein ?? 0,
      carbs: db.carbs ?? 0,
      fat: db.fat ?? 0,
      isHalal: db.isHalal,
      imagePath: db.imagePath,
      // ingredients: db.ingredients,
      date: DateTime.now(),
    );
  }

  @override
  String toString() {
    return '''
FoodModel(
  id: $id,
  dish: $dish,
  englishName: $englishName,
  arabicName: $arabicName,
  serving: $serving,
  calories: $calories,
  protein: $protein,
  carbs: $carbs,
  fat: $fat,
  healthScore: $healthScore,
  isHalal: $isHalal,
  ingredientList: ${ingredientList.map((e) => e.toString()).toList()},
  imagePath: $imagePath,
  date: $date,
  isLoading: $isLoading,
  hasError: $hasError,
  tempId: $tempId,
  remoteLogId: $remoteLogId
)
''';
  }

  FoodModel copyWith({
    Id? id,
    String? dish,
    double? serving,
    int? calories,
    double? protein,
    double? carbs,
    double? fat,
    bool? isHalal,
    DateTime? date,
    String? imagePath,
    bool? isLoading,
    bool? hasError,
    String? englishName,
    String? arabicName,
    String? tempId,
    int? remoteLogId,
    List<Ingredient>? ingredientList,
    int? healthScore,
  }) {
    final copy = FoodModel(
      dish: dish ?? this.dish,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      fat: fat ?? this.fat,
      isHalal: isHalal ?? this.isHalal,
      date: date ?? this.date,
      imagePath: imagePath ?? this.imagePath,
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      englishName: englishName ?? this.englishName,
      arabicName: arabicName ?? this.arabicName,
      tempId: tempId ?? this.tempId,
      ingredientList: ingredientList ?? this.ingredientList,
      remoteLogId: remoteLogId ?? this.remoteLogId,
      serving: serving ?? this.serving,
      healthScore: healthScore ?? this.healthScore,
    );

    copy.id = id ?? this.id;
    return copy;
  }

  factory FoodModel.fromJson(Map<String, dynamic> fullJson) {
    List<Ingredient> parseIngredients(dynamic ingredientsJson) {
      if (ingredientsJson is List<dynamic>) {
        return ingredientsJson.whereType<Map<String, dynamic>>().map((item) => Ingredient.fromJson(item)).toList();
      } else {
        return [];
      }
    }

    final json = fullJson['data'];

    print(parseIngredients(json['ingredients']));

    return FoodModel(
      dish: json['dish'] as String?,
      calories: json['calories'] is int ? json['calories'] as int : int.tryParse(json['calories']?.toString() ?? '') ?? 0,
      protein: (json['protein'] is num) ? (json['protein'] as num).toDouble() : 0.0,
      carbs: (json['carbs'] is num) ? (json['carbs'] as num).toDouble() : 0.0,
      fat: (json['fats'] is num) ? (json['fats'] as num).toDouble() : 0.0,
      isHalal: json['halal'] as bool?,
      englishName: json['english_name'] as String?,
      arabicName: json['arabic_name'] as String?,
      serving: (json['serving'] is num) ? (json['serving'] as num).toDouble() : 1.0,
      healthScore: (json['health_score'] is num) ? (json['health_score'] as num).toInt() : 0,
      ingredientList: parseIngredients(json['ingredients']), // Fixed here
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dish': dish,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'isHalal': isHalal,
      'date': date?.toIso8601String(),
      'imagePath': imagePath,
      'isLoading': isLoading,
      'english_name': englishName,
      'arabic_name': arabicName,
      'ingredients': ingredientList.map((e) => e.toJson()).toList()
    };
  }

  @override
  @ignore
  List<Object?> get props =>
      [dish, calories, protein, carbs, fat, isHalal, date, imagePath, hasError, isLoading, tempId, ingredientList, serving];
}

@embedded
class Ingredient {
  String? englishName;
  String? arabicName;
  int? calories;
  double? protein;
  double? carbs;
  double? fat;

  Ingredient({
    this.englishName,
    this.arabicName,
    this.calories,
    this.protein,
    this.carbs,
    this.fat,
  });

  /// Creates an [Ingredient] from a JSON map.
  factory Ingredient.fromJson(Map<String, dynamic> json) {
    return Ingredient(
      englishName: json['english_name'] as String?,
      arabicName: json['arabic_name'] as String?,
      calories: json['calories'] is int ? json['calories'] : int.tryParse(json['calories'].toString()),
      protein: json['protein'] is num ? (json['protein'] as num).toDouble() : double.tryParse(json['protein'].toString()),
      carbs: json['carbs'] is num ? (json['carbs'] as num).toDouble() : double.tryParse(json['carbs'].toString()),
      fat: json['fat'] is num ? (json['fat'] as num).toDouble() : double.tryParse(json['fat'].toString()),
    );
  }

  /// Converts this [Ingredient] to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'english_name': englishName,
      'arabic_name': arabicName,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
    };
  }
}
